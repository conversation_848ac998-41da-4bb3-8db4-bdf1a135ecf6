<template>
  <div class="app-container">
    <LeftPanel />
    <CanvasEditor />
    <RightPanel />
    <DataSimulator />
  </div>
</template>

<script setup>
import LeftPanel from './components/LeftPanel.vue'
import CanvasEditor from './components/CanvasEditor.vue'
import RightPanel from './components/RightPanel.vue'
import DataSimulator from './components/DataSimulator.vue'
</script>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}
</style>